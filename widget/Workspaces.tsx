import { Gtk } from "astal/gtk4"
import { bind } from "astal"
import Hyprland from "gi://AstalHyprland"

export default function Workspaces() {
    const hypr = Hyprland.get_default()

    return <box cssName="workspaces" spacing={4}>
        {bind(hypr, "workspaces").as(workspaces => 
            workspaces
                .sort((a, b) => a.id - b.id)
                .map(workspace => (
                    <button
                        key={workspace.id}
                        cssClasses={bind(hypr, "focusedWorkspace").as(focused => 
                            focused?.id === workspace.id ? ["workspace", "focused"] : ["workspace"]
                        )}
                        onClicked={() => workspace.focus()}
                        tooltip_text={`工作区 ${workspace.name}`}
                    >
                        <label 
                            label={workspace.name || workspace.id.toString()}
                            cssName="workspace-label"
                        />
                    </button>
                ))
        )}
    </box>
}
